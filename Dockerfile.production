FROM python:3.11-slim

# Set working directory inside container
WORKDIR /app

# Install required system packages including PostgreSQL dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    postgresql-client \
    curl \
    unzip \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Explicitly install PostgreSQL driver to ensure it's available
RUN pip install --no-cache-dir psycopg2-binary==2.9.9

# Verify PostgreSQL driver installation
RUN python -c "import psycopg2; print('✓ psycopg2 successfully imported')"
RUN python -c "from sqlalchemy.dialects import postgresql; print('✓ PostgreSQL dialect available')"

# Copy your local code first
COPY . .

# Copy production config AFTER to ensure it overwrites the development config
COPY rxconfig.production.py rxconfig.py

# Set build environment variable to skip runtime checks
ENV REFLEX_BUILD_ENV=true

# Set dummy database host for build (will be overridden at runtime)
ENV DB_HOST=localhost

# Build the frontend for production deployments
RUN reflex init

# Export the frontend for production
RUN reflex export --frontend-only --no-zip

# Completely unset build environment variable for runtime
ENV REFLEX_BUILD_ENV=

# Override any localhost URLs from .env with production URLs
# Note: API_URL and WEBSOCKET_URL are set via Fly.io secrets for runtime configuration
ENV REDIRECT_URI=https://reflex-chat-bruno-main.fly.dev/auth/callback
ENV BASE_URL=https://reflex-chat-bruno-main.fly.dev
# WEBSOCKET_URL is set via Fly.io secrets to allow runtime configuration

# Force frontend to bind to 0.0.0.0 instead of localhost
ENV HOST=0.0.0.0

# Add debug information for runtime
RUN echo "Build environment variable unset for runtime"

# Expose ports
EXPOSE 3000 8000

# Create a startup script that initializes database after app starts
RUN echo '#!/bin/bash\n\
echo "Starting Reflex application..."\n\
echo "REFLEX_BUILD_ENV: $REFLEX_BUILD_ENV"\n\
echo "HOST environment variable: $HOST"\n\
echo "Frontend host from config should be: 0.0.0.0"\n\
echo "Starting reflex run..."\n\
exec reflex run --env prod --frontend-port 3000 --backend-port 8000 --backend-host 0.0.0.0 --frontend-host 0.0.0.0\n\
' > /app/start-reflex.sh && chmod +x /app/start-reflex.sh

# Production command
CMD ["/app/start-reflex.sh"]
