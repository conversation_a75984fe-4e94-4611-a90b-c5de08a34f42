from sqlmodel import SQLModel, create_engine, Session
import os
from sqlalchemy import text

# Import the models so SQLModel knows about them
from Reflex_Chat.database.models import *

# Check if we're in a build environment
IS_BUILD_ENV = os.getenv("REFLEX_BUILD_ENV", "").lower() == "true"

# Global engine variable
engine = None

def get_database_url():
    """Get the database URL, preferring Fly.io's DATABASE_URL if available."""
    # First try to use Fly.io's DATABASE_URL
    database_url = os.getenv("DATABASE_URL")
    if database_url:
        # Fix for SQLAlchemy 2.x: convert postgres:// to postgresql://
        if database_url.startswith("postgres://"):
            database_url = database_url.replace("postgres://", "postgresql://", 1)
        return database_url

    # Fallback to manual construction for local development
    DB_USER = os.getenv("POSTGRES_USER", "postgres")
    DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "postgres")
    DB_NAME = os.getenv("POSTGRES_DB", "postgres")
    DB_HOST = os.getenv("DB_HOST", "postgres")  # Use env var for host
    DB_PORT = os.getenv("DB_PORT", "5432")

    return f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

def get_engine():
    """Get or create the database engine."""
    global engine
    if engine is None and not IS_BUILD_ENV:
        DATABASE_URL = get_database_url()
        print(f"Connecting to database with URL: {DATABASE_URL}")

        engine = create_engine(
            DATABASE_URL,
            echo=True,  # echo=True shows SQL logs
            pool_pre_ping=True,  # Enable connection health checks
            pool_recycle=3600,  # Recycle connections after 1 hour
        )
    return engine

# Session generator
def get_session():
    engine = get_engine()
    if engine is None:
        raise Exception("Database engine not available (build environment)")
    return Session(engine)

# For dev: create all tables
def create_db_and_tables():
    if IS_BUILD_ENV:
        print("Skipping database table creation in build environment")
        return

    engine = get_engine()
    if engine is None:
        print("Warning: Database engine not available, skipping table creation")
        return

    SQLModel.metadata.create_all(engine)

# Drop and recreate all tables (WARNING: This deletes all data)
def reset_db_and_tables():
    if IS_BUILD_ENV:
        print("Skipping database reset in build environment")
        return

    engine = get_engine()
    if engine is None:
        print("Warning: Database engine not available, skipping database reset")
        return

    print("Dropping all tables and types...")

    # Use a raw connection to execute SQL directly with CASCADE option
    with engine.begin() as conn:
        # First, explicitly drop the problematic table
        conn.execute(text("DROP TABLE IF EXISTS evaluationscore CASCADE"))
        
        # Then drop all remaining tables in the public schema
        conn.execute(text("""
            DO $$ DECLARE
                r RECORD;
            BEGIN
                FOR r IN (SELECT tablename FROM pg_tables WHERE schemaname = 'public') LOOP
                    EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(r.tablename) || ' CASCADE';
                END LOOP;
            END $$;
        """))
        
        # Drop all enum types
        conn.execute(text("DROP TYPE IF EXISTS competencycategory CASCADE"))
        conn.execute(text("DROP TYPE IF EXISTS evaluationtype CASCADE"))
        conn.execute(text("DROP TYPE IF EXISTS evaluatortype CASCADE"))
        conn.execute(text("DROP TYPE IF EXISTS evaluationstatus CASCADE"))
        conn.execute(text("DROP TYPE IF EXISTS projectstatus CASCADE"))
    
    print("Creating all tables and types...")
    SQLModel.metadata.create_all(engine)
    print("Database reset complete.")
