"""
Production-safe database seeding manager for Reflex Chat application.
Provides consistent, repeatable seeding with safety checks and rollback capabilities.
"""

import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import random
import logging
from contextlib import contextmanager

from sqlmodel import Session, select, text, create_engine
from Reflex_Chat.database.db import get_session, get_database_url
from Reflex_Chat.database.models import (
    Role, User, Project, Competency, CompetencyRoleMap, 
    Evaluation, EvaluationQuestion, EvaluationAnswer,
    CompetencyScore, CategoryScore, FactorScore, ActionPlan,
    ProjectStatus, CompetencyCategory, EvaluationType, 
    EvaluatorType, EvaluationStatus, BehaviorType, ResponseType
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SeedingError(Exception):
    """Custom exception for seeding operations"""
    pass

class DatabaseSeeder:
    """Production-safe database seeding manager"""
    
    def __init__(self, environment: str = None):
        self.environment = environment or os.getenv('ENVIRONMENT', 'development')
        self.database_url = get_database_url()
        self.is_production = 'fly' in self.database_url or self.environment == 'production'
        
        logger.info(f"Initializing DatabaseSeeder for {self.environment} environment")
        logger.info(f"Production mode: {self.is_production}")
    
    @contextmanager
    def safe_transaction(self):
        """Context manager for safe database transactions with rollback"""
        session = None
        try:
            session = next(get_session())
            yield session
            session.commit()
            logger.info("Transaction committed successfully")
        except Exception as e:
            if session:
                session.rollback()
                logger.error(f"Transaction rolled back due to error: {e}")
            raise SeedingError(f"Seeding failed: {e}")
        finally:
            if session:
                session.close()
    
    def check_database_state(self) -> Dict[str, int]:
        """Check current database state - returns record counts"""
        with self.safe_transaction() as session:
            counts = {
                'users': len(session.exec(select(User)).all()),
                'roles': len(session.exec(select(Role)).all()),
                'competencies': len(session.exec(select(Competency)).all()),
                'evaluations': len(session.exec(select(Evaluation)).all()),
                'questions': len(session.exec(select(EvaluationQuestion)).all()),
                'scores': len(session.exec(select(CompetencyScore)).all())
            }
            
            logger.info("Current database state:")
            for table, count in counts.items():
                logger.info(f"  {table}: {count} records")
            
            return counts
    
    def is_database_empty(self) -> bool:
        """Check if database is empty (safe to seed)"""
        counts = self.check_database_state()
        return all(count == 0 for count in counts.values())
    
    def backup_database_state(self) -> Dict[str, Any]:
        """Create a backup of current database state (metadata only)"""
        timestamp = datetime.now().isoformat()
        counts = self.check_database_state()
        
        backup_info = {
            'timestamp': timestamp,
            'environment': self.environment,
            'record_counts': counts,
            'database_url_hash': hash(self.database_url)  # Don't store actual URL
        }
        
        # Save backup info to file
        backup_file = f"database_backup_{timestamp.replace(':', '-')}.json"
        with open(backup_file, 'w') as f:
            json.dump(backup_info, f, indent=2)
        
        logger.info(f"Database state backed up to {backup_file}")
        return backup_info
    
    def confirm_production_operation(self, operation: str) -> bool:
        """Require explicit confirmation for production operations"""
        if not self.is_production:
            return True
        
        print(f"\n⚠️  WARNING: You are about to perform '{operation}' on PRODUCTION database!")
        print(f"Database: {self.database_url[:50]}...")
        print(f"Environment: {self.environment}")
        
        confirmation = input("\nType 'CONFIRM' to proceed: ")
        return confirmation == 'CONFIRM'
    
    def seed_database(self, force: bool = False, backup: bool = True) -> bool:
        """
        Main seeding method with safety checks
        
        Args:
            force: Skip safety checks (use with caution)
            backup: Create backup before seeding
        """
        logger.info("Starting database seeding process...")
        
        # Safety checks
        if not force:
            if not self.is_database_empty():
                logger.warning("Database is not empty!")
                if self.is_production:
                    if not self.confirm_production_operation("SEED NON-EMPTY DATABASE"):
                        logger.info("Seeding cancelled by user")
                        return False
        
        # Create backup if requested
        if backup and not self.is_database_empty():
            self.backup_database_state()
        
        # Perform seeding
        try:
            self._execute_seeding()
            logger.info("✅ Database seeding completed successfully!")
            self.check_database_state()  # Show final state
            return True
            
        except Exception as e:
            logger.error(f"❌ Database seeding failed: {e}")
            raise
    
    def _execute_seeding(self):
        """Execute the actual seeding process"""
        from .seed_data import (
            seed_roles, seed_competencies_from_json, 
            seed_competency_role_map_from_json,
            seed_users_and_projects, seed_evaluations
        )
        
        logger.info("Seeding roles...")
        seed_roles()
        
        logger.info("Seeding competencies...")
        seed_competencies_from_json()
        
        logger.info("Seeding competency role mappings...")
        seed_competency_role_map_from_json()
        
        logger.info("Seeding users and projects...")
        seed_users_and_projects()
        
        logger.info("Seeding evaluations...")
        seed_evaluations()
    
    def reset_database(self, confirm: bool = False) -> bool:
        """
        Reset database (DROP ALL DATA) - use with extreme caution
        """
        if not confirm and self.is_production:
            if not self.confirm_production_operation("RESET DATABASE (DELETE ALL DATA)"):
                logger.info("Database reset cancelled by user")
                return False
        
        logger.warning("🚨 RESETTING DATABASE - ALL DATA WILL BE LOST!")
        
        # Create backup before reset
        if not self.is_database_empty():
            self.backup_database_state()
        
        try:
            from Reflex_Chat.database.db import reset_tables
            reset_tables()
            logger.info("✅ Database reset completed")
            return True
        except Exception as e:
            logger.error(f"❌ Database reset failed: {e}")
            raise
    
    def seed_fresh(self) -> bool:
        """Reset and seed database (complete refresh)"""
        logger.info("Starting fresh database seeding (reset + seed)...")
        
        if self.reset_database():
            return self.seed_database(force=True, backup=False)
        return False


# CLI interface functions
def seed_production_database():
    """CLI function for production seeding"""
    seeder = DatabaseSeeder()
    return seeder.seed_database()

def reset_production_database():
    """CLI function for production reset"""
    seeder = DatabaseSeeder()
    return seeder.reset_database()

def fresh_seed_production():
    """CLI function for fresh seeding"""
    seeder = DatabaseSeeder()
    return seeder.seed_fresh()

def check_database_status():
    """CLI function to check database status"""
    seeder = DatabaseSeeder()
    return seeder.check_database_state()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python seed_manager.py [seed|reset|fresh|status]")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "seed":
        seed_production_database()
    elif command == "reset":
        reset_production_database()
    elif command == "fresh":
        fresh_seed_production()
    elif command == "status":
        check_database_status()
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)
