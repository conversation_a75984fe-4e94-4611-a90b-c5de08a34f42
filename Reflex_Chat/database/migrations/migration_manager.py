"""
Database migration management for Reflex Chat application.
Provides schema versioning and migration capabilities.
"""

import os
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging

from sqlmodel import Session, text, select
from Reflex_Chat.database.db import get_session

logger = logging.getLogger(__name__)

class MigrationManager:
    """Manages database schema migrations"""
    
    def __init__(self):
        self.migrations_dir = Path(__file__).parent / "scripts"
        self.migrations_dir.mkdir(exist_ok=True)
        self.ensure_migration_table()
    
    def ensure_migration_table(self):
        """Create migration tracking table if it doesn't exist"""
        with next(get_session()) as session:
            session.exec(text("""
                CREATE TABLE IF NOT EXISTS schema_migrations (
                    id SERIAL PRIMARY KEY,
                    version VARCHAR(50) UNIQUE NOT NULL,
                    name VARCHAR(255) NOT NULL,
                    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    checksum VARCHAR(64),
                    execution_time_ms INTEGER
                )
            """))
            session.commit()
    
    def get_applied_migrations(self) -> List[str]:
        """Get list of applied migration versions"""
        with next(get_session()) as session:
            result = session.exec(text(
                "SELECT version FROM schema_migrations ORDER BY applied_at"
            ))
            return [row[0] for row in result]
    
    def get_pending_migrations(self) -> List[Dict[str, Any]]:
        """Get list of pending migrations"""
        applied = set(self.get_applied_migrations())
        all_migrations = self.discover_migrations()
        
        return [m for m in all_migrations if m['version'] not in applied]
    
    def discover_migrations(self) -> List[Dict[str, Any]]:
        """Discover all migration files"""
        migrations = []
        
        for file_path in sorted(self.migrations_dir.glob("*.sql")):
            # Expected format: V001__create_users_table.sql
            filename = file_path.name
            if filename.startswith('V') and '__' in filename:
                parts = filename.split('__', 1)
                version = parts[0]
                name = parts[1].replace('.sql', '').replace('_', ' ')
                
                migrations.append({
                    'version': version,
                    'name': name,
                    'file_path': file_path,
                    'checksum': self._calculate_checksum(file_path)
                })
        
        return migrations
    
    def _calculate_checksum(self, file_path: Path) -> str:
        """Calculate MD5 checksum of migration file"""
        import hashlib
        with open(file_path, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    
    def create_migration(self, name: str, content: str = None) -> Path:
        """Create a new migration file"""
        # Generate version number
        existing_versions = [m['version'] for m in self.discover_migrations()]
        if existing_versions:
            last_version = max(existing_versions)
            version_num = int(last_version[1:]) + 1
        else:
            version_num = 1
        
        version = f"V{version_num:03d}"
        filename = f"{version}__{name.replace(' ', '_').lower()}.sql"
        file_path = self.migrations_dir / filename
        
        # Create migration file with template
        template = content or f"""-- Migration: {name}
-- Version: {version}
-- Created: {datetime.now().isoformat()}

-- Add your SQL statements here
-- Example:
-- CREATE TABLE example (
--     id SERIAL PRIMARY KEY,
--     name VARCHAR(255) NOT NULL
-- );

-- Remember to test this migration on a copy of your data first!
"""
        
        with open(file_path, 'w') as f:
            f.write(template)
        
        logger.info(f"Created migration: {filename}")
        return file_path
    
    def apply_migration(self, migration: Dict[str, Any]) -> bool:
        """Apply a single migration"""
        logger.info(f"Applying migration {migration['version']}: {migration['name']}")
        
        start_time = datetime.now()
        
        try:
            with next(get_session()) as session:
                # Read and execute migration SQL
                with open(migration['file_path'], 'r') as f:
                    sql_content = f.read()
                
                # Execute migration (split by semicolon for multiple statements)
                statements = [s.strip() for s in sql_content.split(';') if s.strip()]
                for statement in statements:
                    if statement and not statement.startswith('--'):
                        session.exec(text(statement))
                
                # Record migration as applied
                execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
                session.exec(text("""
                    INSERT INTO schema_migrations (version, name, checksum, execution_time_ms)
                    VALUES (:version, :name, :checksum, :execution_time)
                """), {
                    'version': migration['version'],
                    'name': migration['name'],
                    'checksum': migration['checksum'],
                    'execution_time': execution_time
                })
                
                session.commit()
                logger.info(f"✅ Migration {migration['version']} applied successfully ({execution_time}ms)")
                return True
                
        except Exception as e:
            logger.error(f"❌ Migration {migration['version']} failed: {e}")
            raise
    
    def migrate(self, target_version: str = None) -> bool:
        """Apply all pending migrations up to target version"""
        pending = self.get_pending_migrations()
        
        if not pending:
            logger.info("No pending migrations")
            return True
        
        # Filter by target version if specified
        if target_version:
            pending = [m for m in pending if m['version'] <= target_version]
        
        logger.info(f"Applying {len(pending)} migrations...")
        
        for migration in pending:
            if not self.apply_migration(migration):
                return False
        
        logger.info("✅ All migrations applied successfully")
        return True
    
    def rollback_migration(self, version: str) -> bool:
        """Rollback a specific migration (if rollback script exists)"""
        rollback_file = self.migrations_dir / f"{version}__rollback.sql"
        
        if not rollback_file.exists():
            logger.error(f"No rollback script found for {version}")
            return False
        
        logger.info(f"Rolling back migration {version}")
        
        try:
            with next(get_session()) as session:
                with open(rollback_file, 'r') as f:
                    sql_content = f.read()
                
                statements = [s.strip() for s in sql_content.split(';') if s.strip()]
                for statement in statements:
                    if statement and not statement.startswith('--'):
                        session.exec(text(statement))
                
                # Remove from migration history
                session.exec(text(
                    "DELETE FROM schema_migrations WHERE version = :version"
                ), {'version': version})
                
                session.commit()
                logger.info(f"✅ Migration {version} rolled back successfully")
                return True
                
        except Exception as e:
            logger.error(f"❌ Rollback failed: {e}")
            raise
    
    def show_migration_status(self):
        """Display migration status"""
        applied = self.get_applied_migrations()
        pending = self.get_pending_migrations()
        
        print("📊 Migration Status:")
        print(f"   Applied: {len(applied)}")
        print(f"   Pending: {len(pending)}")
        print()
        
        if applied:
            print("✅ Applied Migrations:")
            for version in applied:
                print(f"   {version}")
            print()
        
        if pending:
            print("⏳ Pending Migrations:")
            for migration in pending:
                print(f"   {migration['version']}: {migration['name']}")
        else:
            print("✅ Database is up to date")
    
    def validate_migrations(self) -> bool:
        """Validate migration integrity"""
        logger.info("Validating migration integrity...")
        
        with next(get_session()) as session:
            # Check for checksum mismatches
            applied_migrations = session.exec(text("""
                SELECT version, checksum FROM schema_migrations ORDER BY applied_at
            """)).all()
            
            for version, stored_checksum in applied_migrations:
                migration_file = None
                for m in self.discover_migrations():
                    if m['version'] == version:
                        migration_file = m
                        break
                
                if not migration_file:
                    logger.warning(f"Migration file missing for applied version: {version}")
                    continue
                
                current_checksum = migration_file['checksum']
                if stored_checksum != current_checksum:
                    logger.error(f"Checksum mismatch for {version}: {stored_checksum} != {current_checksum}")
                    return False
        
        logger.info("✅ All migrations validated successfully")
        return True


# CLI functions
def create_migration_cli(name: str):
    """CLI function to create migration"""
    manager = MigrationManager()
    file_path = manager.create_migration(name)
    print(f"Created migration: {file_path}")

def migrate_cli(target_version: str = None):
    """CLI function to apply migrations"""
    manager = MigrationManager()
    manager.migrate(target_version)

def migration_status_cli():
    """CLI function to show migration status"""
    manager = MigrationManager()
    manager.show_migration_status()

def validate_migrations_cli():
    """CLI function to validate migrations"""
    manager = MigrationManager()
    manager.validate_migrations()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python migration_manager.py [create|migrate|status|validate] [args...]")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "create":
        if len(sys.argv) < 3:
            print("Usage: python migration_manager.py create <migration_name>")
            sys.exit(1)
        create_migration_cli(sys.argv[2])
    
    elif command == "migrate":
        target = sys.argv[2] if len(sys.argv) > 2 else None
        migrate_cli(target)
    
    elif command == "status":
        migration_status_cli()
    
    elif command == "validate":
        validate_migrations_cli()
    
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)
