#!/bin/bash

# Database management helper script for Reflex Chat
# Provides easy access to common database operations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Helper functions
print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "$PROJECT_ROOT/rxconfig.py" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Main command handling
case "${1:-help}" in
    "help"|"-h"|"--help")
        echo "Database Management Helper"
        echo ""
        echo "Usage: ./scripts/db.sh <command> [options]"
        echo ""
        echo "Commands:"
        echo "  info          - Show database information"
        echo "  status        - Check database status and record counts"
        echo "  seed          - Seed database with initial data"
        echo "  reset         - Reset database (DESTRUCTIVE!)"
        echo "  fresh         - Reset and seed database"
        echo "  backup        - Create database backup"
        echo "  restore <file> - Restore from backup file"
        echo "  connect       - Connect to database shell"
        echo "  migrate       - Apply database migrations"
        echo "  analyze       - Analyze database content"
        echo "  cleanup       - Clean up old data (dry run)"
        echo "  export        - Export database summary"
        echo ""
        echo "Examples:"
        echo "  ./scripts/db.sh status"
        echo "  ./scripts/db.sh seed"
        echo "  ./scripts/db.sh backup"
        echo "  ./scripts/db.sh restore backup_20231201.sql"
        ;;
    
    "info")
        print_header "Database Information"
        python "$SCRIPT_DIR/db_manager.py" info
        echo ""
        python "$SCRIPT_DIR/db_operations.py" info
        ;;
    
    "status")
        print_header "Database Status"
        python "$SCRIPT_DIR/db_manager.py" status
        echo ""
        python "$SCRIPT_DIR/db_operations.py" analyze
        ;;
    
    "seed")
        print_header "Seeding Database"
        print_warning "This will add data to your database"
        python "$SCRIPT_DIR/db_manager.py" seed
        ;;
    
    "reset")
        print_header "Resetting Database"
        print_error "This will DELETE ALL DATA from your database!"
        read -p "Are you sure? Type 'yes' to continue: " confirm
        if [ "$confirm" = "yes" ]; then
            python "$SCRIPT_DIR/db_manager.py" reset --force
        else
            print_warning "Reset cancelled"
        fi
        ;;
    
    "fresh")
        print_header "Fresh Database Setup"
        print_error "This will RESET and SEED your database!"
        read -p "Are you sure? Type 'yes' to continue: " confirm
        if [ "$confirm" = "yes" ]; then
            python "$SCRIPT_DIR/db_manager.py" fresh
        else
            print_warning "Fresh setup cancelled"
        fi
        ;;
    
    "backup")
        print_header "Creating Database Backup"
        BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
        python "$SCRIPT_DIR/db_manager.py" backup --file "$BACKUP_FILE"
        print_success "Backup created: $BACKUP_FILE"
        ;;
    
    "restore")
        if [ -z "$2" ]; then
            print_error "Please specify backup file: ./scripts/db.sh restore <backup_file>"
            exit 1
        fi
        
        if [ ! -f "$2" ]; then
            print_error "Backup file not found: $2"
            exit 1
        fi
        
        print_header "Restoring Database"
        print_warning "This will overwrite your current database!"
        read -p "Are you sure? Type 'yes' to continue: " confirm
        if [ "$confirm" = "yes" ]; then
            python "$SCRIPT_DIR/db_manager.py" restore --file "$2"
        else
            print_warning "Restore cancelled"
        fi
        ;;
    
    "connect")
        print_header "Connecting to Database"
        python "$SCRIPT_DIR/db_manager.py" connect
        ;;
    
    "shell")
        print_header "Opening Database Shell"
        python "$SCRIPT_DIR/db_manager.py" shell
        ;;
    
    "migrate")
        print_header "Applying Database Migrations"
        cd "$PROJECT_ROOT"
        python -m Reflex_Chat.database.migrations.migration_manager migrate
        ;;
    
    "migration-status")
        print_header "Migration Status"
        cd "$PROJECT_ROOT"
        python -m Reflex_Chat.database.migrations.migration_manager status
        ;;
    
    "create-migration")
        if [ -z "$2" ]; then
            print_error "Please specify migration name: ./scripts/db.sh create-migration <name>"
            exit 1
        fi
        
        print_header "Creating Migration"
        cd "$PROJECT_ROOT"
        python -m Reflex_Chat.database.migrations.migration_manager create "$2"
        ;;
    
    "analyze")
        print_header "Database Analysis"
        python "$SCRIPT_DIR/db_operations.py" analyze
        echo ""
        python "$SCRIPT_DIR/db_operations.py" integrity
        ;;
    
    "sizes")
        print_header "Database Table Sizes"
        python "$SCRIPT_DIR/db_operations.py" sizes
        ;;
    
    "activity")
        DAYS=${2:-7}
        print_header "Recent Activity (Last $DAYS days)"
        python "$SCRIPT_DIR/db_operations.py" activity --days "$DAYS"
        ;;
    
    "cleanup")
        DAYS=${2:-90}
        print_header "Database Cleanup (Dry Run)"
        print_warning "Checking for data older than $DAYS days"
        python "$SCRIPT_DIR/db_operations.py" cleanup --days "$DAYS" --dry-run
        
        echo ""
        read -p "Do you want to actually delete this data? Type 'DELETE' to confirm: " confirm
        if [ "$confirm" = "DELETE" ]; then
            python "$SCRIPT_DIR/db_operations.py" cleanup --days "$DAYS"
        else
            print_warning "Cleanup cancelled"
        fi
        ;;
    
    "export")
        print_header "Exporting Database Summary"
        EXPORT_FILE="db_summary_$(date +%Y%m%d_%H%M%S).json"
        python "$SCRIPT_DIR/db_operations.py" export --output "$EXPORT_FILE"
        print_success "Summary exported: $EXPORT_FILE"
        ;;
    
    "query")
        if [ -z "$2" ]; then
            print_error "Please specify SQL query: ./scripts/db.sh query 'SELECT COUNT(*) FROM users'"
            exit 1
        fi
        
        print_header "Executing Custom Query"
        python "$SCRIPT_DIR/db_operations.py" query --query "$2"
        ;;
    
    "fly-connect")
        print_header "Connecting to Fly.io Database"
        if [ -z "$2" ]; then
            print_error "Please specify app name: ./scripts/db.sh fly-connect <app-name>"
            exit 1
        fi
        
        print_warning "Opening SSH connection to Fly.io..."
        fly ssh console -a "$2" -C "psql \$DATABASE_URL"
        ;;
    
    "fly-backup")
        print_header "Creating Fly.io Database Backup"
        if [ -z "$2" ]; then
            print_error "Please specify app name: ./scripts/db.sh fly-backup <app-name>"
            exit 1
        fi
        
        BACKUP_FILE="fly_backup_$(date +%Y%m%d_%H%M%S).sql"
        print_warning "Creating backup on Fly.io..."
        fly ssh console -a "$2" -C "pg_dump \$DATABASE_URL > $BACKUP_FILE && cat $BACKUP_FILE" > "$BACKUP_FILE"
        print_success "Backup downloaded: $BACKUP_FILE"
        ;;
    
    "fly-seed")
        print_header "Seeding Fly.io Database"
        if [ -z "$2" ]; then
            print_error "Please specify app name: ./scripts/db.sh fly-seed <app-name>"
            exit 1
        fi
        
        print_warning "This will seed the production database!"
        read -p "Are you sure? Type 'SEED' to continue: " confirm
        if [ "$confirm" = "SEED" ]; then
            fly ssh console -a "$2" -C "cd /app && python -m Reflex_Chat.database.seeds.seed_manager seed"
        else
            print_warning "Seeding cancelled"
        fi
        ;;
    
    *)
        print_error "Unknown command: $1"
        echo "Run './scripts/db.sh help' for usage information"
        exit 1
        ;;
esac
