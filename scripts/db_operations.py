#!/usr/bin/env python3
"""
Database operations and utilities for Reflex Chat application.
Provides common database tasks and monitoring capabilities.
"""

import os
import sys
from pathlib import Path
from datetime import datetime, timedelta
import json

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlmodel import Session, select, text, func
from Reflex_Chat.database.db import get_session, get_database_url
from Reflex_Chat.database.models import *

class DatabaseOperations:
    """Common database operations and utilities"""
    
    def __init__(self):
        self.db_url = get_database_url()
        self.is_production = 'fly' in self.db_url
    
    def get_table_sizes(self):
        """Get size information for all tables"""
        print("📊 Database Table Sizes:")
        
        with next(get_session()) as session:
            # Get table sizes (PostgreSQL specific)
            result = session.exec(text("""
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
                FROM pg_tables 
                WHERE schemaname = 'public'
                ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
            """))
            
            for row in result:
                schema, table, size, size_bytes = row
                print(f"   {table}: {size}")
    
    def get_connection_info(self):
        """Get database connection information"""
        print("🔗 Database Connection Info:")
        
        with next(get_session()) as session:
            # Get database version
            version = session.exec(text("SELECT version()")).first()
            print(f"   PostgreSQL Version: {version[0][:50]}...")
            
            # Get current database name
            db_name = session.exec(text("SELECT current_database()")).first()
            print(f"   Database Name: {db_name[0]}")
            
            # Get connection count
            connections = session.exec(text("""
                SELECT count(*) FROM pg_stat_activity 
                WHERE state = 'active'
            """)).first()
            print(f"   Active Connections: {connections[0]}")
    
    def analyze_user_data(self):
        """Analyze user and evaluation data"""
        print("👥 User Data Analysis:")
        
        with next(get_session()) as session:
            # User counts by role
            user_roles = session.exec(text("""
                SELECT r.name, COUNT(u.id) as user_count
                FROM role r
                LEFT JOIN "user" u ON r.id = u.role_id
                GROUP BY r.name
                ORDER BY user_count DESC
            """)).all()
            
            print("   Users by Role:")
            for role, count in user_roles:
                print(f"     {role}: {count}")
            
            # Evaluation statistics
            eval_stats = session.exec(text("""
                SELECT 
                    evaluation_type,
                    status,
                    COUNT(*) as count
                FROM evaluation
                GROUP BY evaluation_type, status
                ORDER BY evaluation_type, status
            """)).all()
            
            print("\n   Evaluations by Type & Status:")
            for eval_type, status, count in eval_stats:
                print(f"     {eval_type} - {status}: {count}")
    
    def check_data_integrity(self):
        """Check data integrity and relationships"""
        print("🔍 Data Integrity Check:")
        
        with next(get_session()) as session:
            # Check for orphaned records
            orphaned_checks = [
                ("Users without roles", """
                    SELECT COUNT(*) FROM "user" WHERE role_id IS NOT NULL 
                    AND role_id NOT IN (SELECT id FROM role)
                """),
                ("Evaluations without users", """
                    SELECT COUNT(*) FROM evaluation WHERE user_id NOT IN (SELECT id FROM "user")
                """),
                ("Scores without evaluations", """
                    SELECT COUNT(*) FROM competencyscore 
                    WHERE evaluation_id NOT IN (SELECT id FROM evaluation)
                """),
                ("Questions without competencies", """
                    SELECT COUNT(*) FROM evaluationquestion 
                    WHERE competency_id NOT IN (SELECT id FROM competency)
                """)
            ]
            
            for check_name, query in orphaned_checks:
                count = session.exec(text(query)).first()[0]
                status = "✅" if count == 0 else "❌"
                print(f"   {status} {check_name}: {count}")
    
    def get_recent_activity(self, days: int = 7):
        """Get recent database activity"""
        print(f"📈 Recent Activity (Last {days} days):")
        
        cutoff_date = datetime.now() - timedelta(days=days)
        
        with next(get_session()) as session:
            # Recent evaluations
            recent_evals = session.exec(text("""
                SELECT DATE(created_at) as date, COUNT(*) as count
                FROM evaluation
                WHERE created_at >= :cutoff_date
                GROUP BY DATE(created_at)
                ORDER BY date DESC
            """), {"cutoff_date": cutoff_date}).all()
            
            if recent_evals:
                print("   Recent Evaluations:")
                for date, count in recent_evals:
                    print(f"     {date}: {count}")
            else:
                print("   No recent evaluations")
            
            # Recent user logins
            recent_logins = session.exec(text("""
                SELECT DATE(last_login) as date, COUNT(*) as count
                FROM "user"
                WHERE last_login >= :cutoff_date
                GROUP BY DATE(last_login)
                ORDER BY date DESC
            """), {"cutoff_date": cutoff_date}).all()
            
            if recent_logins:
                print("\n   Recent User Logins:")
                for date, count in recent_logins:
                    print(f"     {date}: {count}")
            else:
                print("\n   No recent user logins")
    
    def cleanup_old_data(self, days: int = 90, dry_run: bool = True):
        """Clean up old data (with dry run option)"""
        print(f"🧹 Cleanup Old Data (older than {days} days):")
        
        cutoff_date = datetime.now() - timedelta(days=days)
        
        if self.is_production and not dry_run:
            confirm = input("⚠️  This will DELETE data from production! Type 'DELETE' to confirm: ")
            if confirm != 'DELETE':
                print("Cleanup cancelled")
                return
        
        with next(get_session()) as session:
            # Find old draft evaluations
            old_drafts = session.exec(text("""
                SELECT COUNT(*) FROM evaluation
                WHERE status = 'DRAFT' AND created_at < :cutoff_date
            """), {"cutoff_date": cutoff_date}).first()[0]
            
            print(f"   Old draft evaluations to clean: {old_drafts}")
            
            if not dry_run and old_drafts > 0:
                session.exec(text("""
                    DELETE FROM evaluation
                    WHERE status = 'DRAFT' AND created_at < :cutoff_date
                """), {"cutoff_date": cutoff_date})
                session.commit()
                print(f"   ✅ Deleted {old_drafts} old draft evaluations")
            elif dry_run:
                print("   (Dry run - no data deleted)")
    
    def export_data_summary(self, output_file: str = None):
        """Export database summary to JSON"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"db_summary_{timestamp}.json"
        
        print(f"📤 Exporting database summary to: {output_file}")
        
        with next(get_session()) as session:
            summary = {
                "timestamp": datetime.now().isoformat(),
                "database_url": self.db_url[:50] + "...",
                "is_production": self.is_production,
                "table_counts": {},
                "recent_activity": {},
                "data_integrity": {}
            }
            
            # Table counts
            tables = ['user', 'role', 'competency', 'evaluation', 'evaluationquestion', 
                     'competencyscore', 'categoryscore', 'factorscore']
            
            for table in tables:
                count = session.exec(text(f'SELECT COUNT(*) FROM "{table}"')).first()[0]
                summary["table_counts"][table] = count
            
            # Recent activity (last 30 days)
            cutoff = datetime.now() - timedelta(days=30)
            recent_evals = session.exec(text("""
                SELECT COUNT(*) FROM evaluation WHERE created_at >= :cutoff
            """), {"cutoff": cutoff}).first()[0]
            summary["recent_activity"]["evaluations_last_30_days"] = recent_evals
            
            # Save to file
            with open(output_file, 'w') as f:
                json.dump(summary, f, indent=2)
            
            print(f"✅ Summary exported to {output_file}")
    
    def run_custom_query(self, query: str, params: dict = None):
        """Run custom SQL query"""
        print(f"🔧 Executing custom query:")
        print(f"   {query[:100]}...")
        
        try:
            with next(get_session()) as session:
                result = session.exec(text(query), params or {})
                
                # Handle different result types
                if query.strip().upper().startswith('SELECT'):
                    rows = result.all()
                    print(f"✅ Query returned {len(rows)} rows")
                    
                    # Show first few rows
                    for i, row in enumerate(rows[:5]):
                        print(f"   Row {i+1}: {row}")
                    
                    if len(rows) > 5:
                        print(f"   ... and {len(rows) - 5} more rows")
                else:
                    session.commit()
                    print("✅ Query executed successfully")
                    
        except Exception as e:
            print(f"❌ Query failed: {e}")


def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Database Operations CLI")
    parser.add_argument('command', choices=[
        'info', 'sizes', 'analyze', 'integrity', 'activity', 
        'cleanup', 'export', 'query'
    ], help='Operation to perform')
    
    parser.add_argument('--days', type=int, default=7, help='Number of days for activity/cleanup')
    parser.add_argument('--output', help='Output file path')
    parser.add_argument('--query', help='SQL query to execute')
    parser.add_argument('--dry-run', action='store_true', help='Dry run for cleanup operations')
    
    args = parser.parse_args()
    
    db_ops = DatabaseOperations()
    
    try:
        if args.command == 'info':
            db_ops.get_connection_info()
        
        elif args.command == 'sizes':
            db_ops.get_table_sizes()
        
        elif args.command == 'analyze':
            db_ops.analyze_user_data()
        
        elif args.command == 'integrity':
            db_ops.check_data_integrity()
        
        elif args.command == 'activity':
            db_ops.get_recent_activity(args.days)
        
        elif args.command == 'cleanup':
            db_ops.cleanup_old_data(args.days, args.dry_run)
        
        elif args.command == 'export':
            db_ops.export_data_summary(args.output)
        
        elif args.command == 'query':
            if not args.query:
                print("❌ --query required for query command")
                sys.exit(1)
            db_ops.run_custom_query(args.query)
    
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
