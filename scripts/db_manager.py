#!/usr/bin/env python3
"""
Database management CLI for Reflex Chat application.
Provides safe database operations for both development and production.
"""

import os
import sys
import argparse
import subprocess
import json
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from Reflex_Chat.database.seeds.seed_manager import DatabaseSeeder
from Reflex_Chat.database.db import get_database_url

class DatabaseManager:
    """Database management utilities"""
    
    def __init__(self):
        self.seeder = DatabaseSeeder()
        self.db_url = get_database_url()
        self.is_fly = 'fly' in self.db_url
    
    def connect_to_database(self):
        """Connect to database using appropriate method"""
        if self.is_fly:
            print("🚀 Connecting to Fly.io PostgreSQL...")
            # Extract connection details from URL
            # postgresql://user:pass@host:port/db
            parts = self.db_url.replace('postgresql://', '').split('@')
            if len(parts) == 2:
                user_pass = parts[0]
                host_port_db = parts[1]
                
                user = user_pass.split(':')[0]
                host = host_port_db.split(':')[0]
                
                print(f"Host: {host}")
                print(f"User: {user}")
                print("\n💡 To connect manually:")
                print(f"   fly ssh console -a <your-app-name>")
                print(f"   psql {self.db_url}")
        else:
            print("🏠 Connecting to local PostgreSQL...")
            print(f"Database URL: {self.db_url}")
    
    def backup_database(self, output_file: str = None):
        """Create database backup"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"backup_{timestamp}.sql"
        
        print(f"📦 Creating database backup: {output_file}")
        
        if self.is_fly:
            print("For Fly.io databases, use:")
            print(f"   fly ssh console -a <your-app-name>")
            print(f"   pg_dump {self.db_url} > {output_file}")
        else:
            # Local backup
            try:
                cmd = f"pg_dump {self.db_url} > {output_file}"
                subprocess.run(cmd, shell=True, check=True)
                print(f"✅ Backup created: {output_file}")
            except subprocess.CalledProcessError as e:
                print(f"❌ Backup failed: {e}")
    
    def restore_database(self, backup_file: str):
        """Restore database from backup"""
        if not os.path.exists(backup_file):
            print(f"❌ Backup file not found: {backup_file}")
            return
        
        print(f"📥 Restoring database from: {backup_file}")
        
        if self.is_fly:
            print("For Fly.io databases, use:")
            print(f"   fly ssh console -a <your-app-name>")
            print(f"   psql {self.db_url} < {backup_file}")
        else:
            try:
                cmd = f"psql {self.db_url} < {backup_file}"
                subprocess.run(cmd, shell=True, check=True)
                print("✅ Database restored successfully")
            except subprocess.CalledProcessError as e:
                print(f"❌ Restore failed: {e}")
    
    def run_sql_file(self, sql_file: str):
        """Execute SQL file against database"""
        if not os.path.exists(sql_file):
            print(f"❌ SQL file not found: {sql_file}")
            return
        
        print(f"🔧 Executing SQL file: {sql_file}")
        
        if self.is_fly:
            print("For Fly.io databases, use:")
            print(f"   fly ssh console -a <your-app-name>")
            print(f"   psql {self.db_url} -f {sql_file}")
        else:
            try:
                cmd = f"psql {self.db_url} -f {sql_file}"
                subprocess.run(cmd, shell=True, check=True)
                print("✅ SQL file executed successfully")
            except subprocess.CalledProcessError as e:
                print(f"❌ SQL execution failed: {e}")
    
    def run_python_script(self, script_path: str):
        """Execute Python script with database access"""
        if not os.path.exists(script_path):
            print(f"❌ Python script not found: {script_path}")
            return
        
        print(f"🐍 Executing Python script: {script_path}")
        
        if self.is_fly:
            print("For Fly.io, upload script and run:")
            print(f"   fly ssh console -a <your-app-name>")
            print(f"   python {script_path}")
        else:
            try:
                subprocess.run([sys.executable, script_path], check=True)
                print("✅ Python script executed successfully")
            except subprocess.CalledProcessError as e:
                print(f"❌ Script execution failed: {e}")
    
    def show_database_info(self):
        """Display database information"""
        print("📊 Database Information:")
        print(f"   URL: {self.db_url[:50]}...")
        print(f"   Environment: {self.seeder.environment}")
        print(f"   Production: {self.seeder.is_production}")
        print()
        
        # Show table counts
        try:
            counts = self.seeder.check_database_state()
            print("📈 Table Record Counts:")
            for table, count in counts.items():
                print(f"   {table}: {count}")
        except Exception as e:
            print(f"❌ Could not retrieve table counts: {e}")
    
    def interactive_shell(self):
        """Start interactive database shell"""
        print("🔧 Starting interactive database shell...")
        
        if self.is_fly:
            print("For Fly.io databases:")
            print("1. fly ssh console -a <your-app-name>")
            print("2. psql $DATABASE_URL")
        else:
            try:
                subprocess.run(f"psql {self.db_url}", shell=True)
            except KeyboardInterrupt:
                print("\n👋 Exiting database shell")


def main():
    parser = argparse.ArgumentParser(description="Database Management CLI")
    parser.add_argument('command', choices=[
        'info', 'seed', 'reset', 'fresh', 'status', 
        'backup', 'restore', 'connect', 'shell', 'run-sql', 'run-python'
    ], help='Command to execute')
    
    parser.add_argument('--file', help='File path for backup/restore/run operations')
    parser.add_argument('--force', action='store_true', help='Force operation without confirmation')
    
    args = parser.parse_args()
    
    db_manager = DatabaseManager()
    
    try:
        if args.command == 'info':
            db_manager.show_database_info()
        
        elif args.command == 'seed':
            db_manager.seeder.seed_database(force=args.force)
        
        elif args.command == 'reset':
            db_manager.seeder.reset_database(confirm=args.force)
        
        elif args.command == 'fresh':
            db_manager.seeder.seed_fresh()
        
        elif args.command == 'status':
            db_manager.seeder.check_database_state()
        
        elif args.command == 'backup':
            db_manager.backup_database(args.file)
        
        elif args.command == 'restore':
            if not args.file:
                print("❌ --file required for restore command")
                sys.exit(1)
            db_manager.restore_database(args.file)
        
        elif args.command == 'connect':
            db_manager.connect_to_database()
        
        elif args.command == 'shell':
            db_manager.interactive_shell()
        
        elif args.command == 'run-sql':
            if not args.file:
                print("❌ --file required for run-sql command")
                sys.exit(1)
            db_manager.run_sql_file(args.file)
        
        elif args.command == 'run-python':
            if not args.file:
                print("❌ --file required for run-python command")
                sys.exit(1)
            db_manager.run_python_script(args.file)
    
    except KeyboardInterrupt:
        print("\n👋 Operation cancelled by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
