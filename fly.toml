# Fly.io configuration for main Reflex application
app = "reflex-chat-main"
primary_region = "iad"

[build]
  dockerfile = "Dockerfile.production"

[env]
  PYTHONUNBUFFERED = "1"
  FRONTEND_PORT = "3000"
  BACKEND_PORT = "8000"
  REFLEX_CONFIG = "rxconfig.production"

# Primary HTTP service for frontend proxy (port 3001)
[http_service]
  internal_port = 3001
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

  [[http_service.checks]]
    grace_period = "90s"
    interval = "30s"
    method = "GET"
    timeout = "20s"
    path = "/health"

[http_service.concurrency]
  type = "connections"
  hard_limit = 1000
  soft_limit = 500

# Backend WebSocket service on port 8000 (internal only)
# Note: This service is for internal WebSocket connections
# The health check is handled by the primary http_service on port 3000
[[services]]
  protocol = "tcp"
  internal_port = 8000

  [[services.ports]]
    port = 8000
    handlers = ["tls", "http"]

  # Remove health checks for backend service to avoid conflicts
  # Health checks are handled by the primary http_service

[[vm]]
  memory = "1gb"
  cpu_kind = "shared"
  cpus = 1
