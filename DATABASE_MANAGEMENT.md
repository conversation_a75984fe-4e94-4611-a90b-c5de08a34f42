# Database Management Guide

Complete guide for managing your Reflex application database on Fly.io with PostgreSQL.

## 🚀 Quick Start

### Check Database Status
```bash
python scripts/db_manager.py info
python scripts/db_operations.py analyze
```

### Seed Production Database
```bash
python scripts/db_manager.py seed
```

### Connect to Production Database
```bash
# Get connection info
python scripts/db_manager.py connect

# Direct connection (requires fly CLI)
fly ssh console -a <your-app-name>
psql $DATABASE_URL
```

## 1. 📊 Database Seeding Strategy

### Production-Safe Seeding

The `DatabaseSeeder` class provides safe, repeatable seeding with built-in safety checks:

```python
from Reflex_Chat.database.seeds.seed_manager import DatabaseSeeder

# Initialize seeder (auto-detects environment)
seeder = DatabaseSeeder()

# Check database state
seeder.check_database_state()

# Seed database (with safety checks)
seeder.seed_database()

# Reset and seed fresh (DESTRUCTIVE!)
seeder.seed_fresh()
```

### CLI Commands

```bash
# Seed database
python scripts/db_manager.py seed

# Reset database (DESTRUCTIVE!)
python scripts/db_manager.py reset

# Fresh seed (reset + seed)
python scripts/db_manager.py fresh

# Check status
python scripts/db_manager.py status
```

### Safety Features

- **Environment Detection**: Automatically detects production vs development
- **Confirmation Prompts**: Requires explicit confirmation for destructive operations
- **Backup Creation**: Creates backups before major operations
- **Transaction Safety**: Uses transactions with rollback on failure
- **State Validation**: Checks database state before operations

## 2. 🔄 Database Schema Management

### Migration System

Use the `MigrationManager` for schema changes:

```python
from Reflex_Chat.database.migrations.migration_manager import MigrationManager

manager = MigrationManager()

# Create new migration
manager.create_migration("add_user_preferences_table")

# Apply all pending migrations
manager.migrate()

# Check migration status
manager.show_migration_status()
```

### Migration Best Practices

1. **Always Test Locally First**
   ```bash
   # Test migration on local copy
   python -m Reflex_Chat.database.migrations.migration_manager migrate
   ```

2. **Create Rollback Scripts**
   - For each `V001__migration.sql`, create `V001__rollback.sql`
   - Test rollback procedures

3. **Use Transactions**
   ```sql
   BEGIN;
   -- Your migration SQL here
   COMMIT;
   ```

4. **Backup Before Major Changes**
   ```bash
   python scripts/db_manager.py backup --file pre_migration_backup.sql
   ```

### Migration File Structure
```
Reflex_Chat/database/migrations/scripts/
├── V001__create_initial_tables.sql
├── V001__rollback.sql
├── V002__add_user_preferences.sql
├── V002__rollback.sql
└── ...
```

## 3. 🔧 Database Operations

### Direct Database Access

#### For Fly.io Production:
```bash
# SSH into production container
fly ssh console -a <your-app-name>

# Connect to PostgreSQL
psql $DATABASE_URL

# Or combine both
fly ssh console -a <your-app-name> -C "psql $DATABASE_URL"
```

#### For Local Development:
```bash
# Direct connection
psql $DATABASE_URL

# Or use our CLI
python scripts/db_manager.py shell
```

### Execute Python Scripts on Production

1. **Upload Script to Production**:
   ```bash
   # Create a temporary script
   echo "from Reflex_Chat.database.seeds.seed_manager import check_database_status; check_database_status()" > temp_script.py
   
   # SSH and run
   fly ssh console -a <your-app-name>
   python temp_script.py
   ```

2. **Use Database Operations CLI**:
   ```bash
   python scripts/db_operations.py query --query "SELECT COUNT(*) FROM users"
   ```

### Backup and Restore

#### Create Backup:
```bash
# Local backup
python scripts/db_manager.py backup --file my_backup.sql

# Production backup (manual)
fly ssh console -a <your-app-name>
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d).sql
```

#### Restore Backup:
```bash
# Local restore
python scripts/db_manager.py restore --file my_backup.sql

# Production restore (manual)
fly ssh console -a <your-app-name>
psql $DATABASE_URL < backup_20231201.sql
```

## 4. 🔄 Development vs Production Consistency

### Recommended Workflow

1. **Local Development**:
   ```bash
   # Make schema changes locally
   python -m Reflex_Chat.database.migrations.migration_manager create "add_new_feature"
   
   # Edit the migration file
   # Test migration
   python -m Reflex_Chat.database.migrations.migration_manager migrate
   
   # Test your application
   # Create rollback script if needed
   ```

2. **Staging/Testing**:
   ```bash
   # Deploy to staging environment
   # Test migration on staging data
   python scripts/db_manager.py backup  # backup first
   python -m Reflex_Chat.database.migrations.migration_manager migrate
   ```

3. **Production Deployment**:
   ```bash
   # Backup production
   fly ssh console -a <your-app-name> -C "pg_dump $DATABASE_URL > pre_deploy_backup.sql"
   
   # Apply migrations
   fly ssh console -a <your-app-name>
   python -m Reflex_Chat.database.migrations.migration_manager migrate
   ```

### Environment Synchronization

```bash
# Export production schema
fly ssh console -a <your-app-name> -C "pg_dump --schema-only $DATABASE_URL > prod_schema.sql"

# Apply to local development
psql $LOCAL_DATABASE_URL < prod_schema.sql

# Or use our export tool
python scripts/db_operations.py export --output prod_summary.json
```

## 5. 📈 Monitoring and Maintenance

### Health Checks

```bash
# Database connection and basic info
python scripts/db_operations.py info

# Table sizes and storage usage
python scripts/db_operations.py sizes

# Data integrity checks
python scripts/db_operations.py integrity

# Recent activity analysis
python scripts/db_operations.py activity --days 30
```

### Regular Maintenance Tasks

#### Weekly:
```bash
# Check database health
python scripts/db_operations.py analyze

# Review recent activity
python scripts/db_operations.py activity --days 7

# Export summary for records
python scripts/db_operations.py export
```

#### Monthly:
```bash
# Clean up old data (dry run first)
python scripts/db_operations.py cleanup --days 90 --dry-run
python scripts/db_operations.py cleanup --days 90

# Full backup
python scripts/db_manager.py backup --file monthly_backup_$(date +%Y%m).sql

# Validate migrations
python -m Reflex_Chat.database.migrations.migration_manager validate
```

### Monitoring Queries

```sql
-- Active connections
SELECT count(*) FROM pg_stat_activity WHERE state = 'active';

-- Database size
SELECT pg_size_pretty(pg_database_size(current_database()));

-- Largest tables
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Recent activity
SELECT DATE(created_at) as date, COUNT(*) as evaluations
FROM evaluation
WHERE created_at >= NOW() - INTERVAL '7 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

## 🚨 Emergency Procedures

### Database Recovery

1. **Identify Issue**:
   ```bash
   python scripts/db_operations.py integrity
   python scripts/db_operations.py info
   ```

2. **Stop Application** (if needed):
   ```bash
   fly scale count 0 -a <your-app-name>
   ```

3. **Restore from Backup**:
   ```bash
   fly ssh console -a <your-app-name>
   psql $DATABASE_URL < latest_backup.sql
   ```

4. **Restart Application**:
   ```bash
   fly scale count 1 -a <your-app-name>
   ```

### Migration Rollback

```bash
# Rollback specific migration
python -m Reflex_Chat.database.migrations.migration_manager rollback V003

# Or restore from pre-migration backup
python scripts/db_manager.py restore --file pre_migration_backup.sql
```

## 📝 Best Practices Summary

1. **Always backup before major operations**
2. **Test migrations locally first**
3. **Use transactions for safety**
4. **Monitor database health regularly**
5. **Keep development and production schemas in sync**
6. **Document all schema changes**
7. **Use environment-specific safety checks**
8. **Maintain rollback procedures**
9. **Regular cleanup of old data**
10. **Monitor performance and usage**
